"use client";
import React from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

const Footer = () => {
  const router = useRouter();

  const handleRedirect = (event: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => {
    event.preventDefault();
    const href = event.currentTarget.getAttribute('href');
    if (href) {
      router.push(href);
    }
  };

  return (
    <footer
      className="py-16"
      style={{
        backgroundImage: "url('/image 18.svg')",
        backgroundSize: 'cover',
        backgroundPosition: 'center',
      }}
    >
      <div className="max-w-6xl mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="font-bold text-lg mb-4">ABOUT</h3>
            <ul className="space-y-2">
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; About Us</a></li>
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; Contact Us</a></li>
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; Latest Blog</a></li>
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; Authenticity Guarantee</a></li>
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; Customer Reviews</a></li>
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; Privacy Policy</a></li>
            </ul>
          </div>
          <div>
            <h3 className="font-bold text-lg mb-4">MY ACCOUNT</h3>
            <ul className="space-y-2">
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; Manage Your Account</a></li>
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; How To Deposit</a></li>
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; How To Withdraw</a></li>
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; Account Verification</a></li>
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; Safety & Security</a></li>
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; Membership Level</a></li>
            </ul>
          </div>
          <div>
            <h3 className="font-bold text-lg mb-4">HELP CENTER</h3>
            <ul className="space-y-2">
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; Help Center</a></li>
              <li><Link href="/faq" className="text-gray-600 hover:underline">&raquo; FAQ</Link></li>
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; Quick Start Guide</a></li>
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; Tutorials</a></li>
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; Borrow</a></li>
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; Lend</a></li>
            </ul>
          </div>
          <div>
            <h3 className="font-bold text-lg mb-4">LEGAL INFO</h3>
            <ul className="space-y-2">
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; Risk Warnings</a></li>
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; Privacy Notice</a></li>
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; Security</a></li>
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; Terms Of Service</a></li>
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; Become Affiliate</a></li>
              <li><a href="#" onClick={handleRedirect} className="text-gray-600 hover:underline">&raquo; Complaints Policy</a></li>
            </ul>
          </div>
        </div>
        <div className="text-center text-gray-500 mt-16 border-t border-gray-200 pt-8">
          Copyright &copy; 2025. All Rights Reserved By Kredxa
        </div>
      </div>
    </footer>
  );
};

export default Footer;
