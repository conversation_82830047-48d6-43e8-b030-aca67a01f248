import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import Link from 'next/link';

export default function CreateOfferSuccessPage() {
  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Success Header */}
      <div className="text-center space-y-4">
        <div className="flex justify-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
        </div>
        <h1 className="text-2xl font-bold text-gray-900">Loan Offer Created Successfully!</h1>
        <p className="text-gray-600">
          Your loan offer has been published to the marketplace and is now visible to potential borrowers.
        </p>
      </div>

      {/* Next Steps Card */}
      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold text-gray-900">What happens next?</h2>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-blue-600 text-xs font-bold">1</span>
            </div>
            <div>
              <h3 className="font-medium text-gray-900">Borrowers can view your offer</h3>
              <p className="text-sm text-gray-600">
                Your loan offer is now live on the marketplace and borrowers can see it when browsing available loans.
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-blue-600 text-xs font-bold">2</span>
            </div>
            <div>
              <h3 className="font-medium text-gray-900">You&apos;ll receive applications</h3>
              <p className="text-sm text-gray-600">
                When borrowers apply for your loan, you&apos;ll receive notifications and can review their applications.
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-blue-600 text-xs font-bold">3</span>
            </div>
            <div>
              <h3 className="font-medium text-gray-900">Review and approve</h3>
              <p className="text-sm text-gray-600">
                Review borrower applications and approve those that meet your criteria. Kredxa handles the rest!
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Link href="/dashboard/lender/portfolio" className="flex-1">
          <Button variant="outline" className="w-full">
            <Eye className="w-4 h-4 mr-2" />
            View My Portfolio
          </Button>
        </Link>
        <Link href="/dashboard/lender/create-offer" className="flex-1">
          <Button className="w-full bg-blue-600 hover:bg-blue-700">
            <ArrowRight className="w-4 h-4 mr-2" />
            Create Another Offer
          </Button>
        </Link>
      </div>

      {/* Additional Info */}
      <div className="bg-blue-50 rounded-lg p-4">
        <h3 className="font-medium text-blue-900 mb-2">Need help?</h3>
        <p className="text-sm text-blue-800">
          If you have any questions about your loan offer or need to make changes, 
          contact our support team or visit the help center.
        </p>
        <div className="mt-3 flex gap-2">
          <Link href="/dashboard/support">
            <Button variant="ghost" size="sm" className="text-blue-700 hover:text-blue-800">
              Contact Support
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
} 