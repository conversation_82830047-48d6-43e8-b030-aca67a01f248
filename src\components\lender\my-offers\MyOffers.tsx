"use client"

import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { 
  Eye, 
  Edit, 
  Pause, 
  Play, 
  Trash2, 
  Users, 
  Tag,
  BanknoteArrowDown,
  Plus,
  ArrowLeft,
  X,
  Save,
  Loader2,
  FileText,
  Clock,
  CheckCircle,
  XCircle
} from "lucide-react";
import Link from 'next/link';
import { getLenderOffers, updateLoanOffer, deleteLoanOffer } from '@/lib/loan-offers';
import { getLoanOfferApplications, updateApplicationStatus } from '@/lib/loan-applications';
import { Database } from '@/lib/supabase';
import { toast } from 'sonner';

const targetBorrowerOptions = [
  "Individual borrowers",
  "Small business owners",
  "Startups",
  "Established businesses",
  "Freelancers",
  "Students"
];

type LoanOffer = Database['public']['Tables']['loan_offers']['Row'];
type LoanOfferUpdate = Database['public']['Tables']['loan_offers']['Update'];
type LoanApplication = Database['public']['Tables']['loan_applications']['Row'] & {
  applicant_name?: string;
};

export default function MyOffers() {
  const [offers, setOffers] = useState<LoanOffer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [applicationsModalOpen, setApplicationsModalOpen] = useState(false);
  const [applicationViewModalOpen, setApplicationViewModalOpen] = useState(false);
  const [selectedOffer, setSelectedOffer] = useState<LoanOffer | null>(null);
  const [editFormData, setEditFormData] = useState<LoanOfferUpdate | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [applications, setApplications] = useState<LoanApplication[]>([]);
  const [selectedApplication, setSelectedApplication] = useState<LoanApplication | null>(null);
  const [applicationsLoading, setApplicationsLoading] = useState(false);
  const [applicationCounts, setApplicationCounts] = useState<Record<string, number>>({});

  const fetchApplicationCounts = useCallback(async (offersList: LoanOffer[]) => {
    try {
      const counts: Record<string, number> = {};
      
      // Fetch application counts for each offer
      await Promise.all(
        offersList.map(async (offer) => {
          try {
            const result = await getLoanOfferApplications(offer.id);
            if (result.success && result.data) {
              counts[offer.id] = Array.isArray(result.data) ? result.data.length : 1;
            } else {
              counts[offer.id] = 0;
            }
          } catch (error) {
            console.error(`Error fetching applications for offer ${offer.id}:`, error);
            counts[offer.id] = 0;
          }
        })
      );
      
      setApplicationCounts(counts);
    } catch (error) {
      console.error('Error fetching application counts:', error);
    }
  }, []);

  const fetchOffers = useCallback(async () => {
    try {
      setIsLoading(true);
      const result = await getLenderOffers();
      if (result.success) {
        setOffers(result.data || []);
        // Fetch application counts after offers are loaded
        await fetchApplicationCounts(result.data || []);
      } else {
        toast.error(result.error || 'Failed to fetch offers');
      }
    } catch (error) {
      console.error('Error fetching offers:', error);
      toast.error('An error occurred while fetching offers');
    } finally {
      setIsLoading(false);
    }
  }, [fetchApplicationCounts]);

  // Fetch offers on component mount
  useEffect(() => {
    fetchOffers();
  }, [fetchOffers]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <Play className="w-4 h-4" />;
      case 'paused':
        return <Pause className="w-4 h-4" />;
      case 'draft':
        return <Edit className="w-4 h-4" />;
      default:
        return <Edit className="w-4 h-4" />;
    }
  };

  const toggleOfferStatus = async (offerId: string) => {
    try {
      const offer = offers.find(o => o.id === offerId);
      if (!offer) return;

      const newStatus = offer.status === 'active' ? 'paused' : 'active';
      const result = await updateLoanOffer(offerId, { status: newStatus });

      if (result.success) {
        setOffers(prev => prev.map(o => 
          o.id === offerId ? { ...o, status: newStatus } : o
        ));
        toast.success(`Offer ${newStatus === 'active' ? 'activated' : 'paused'} successfully`);
      } else {
        toast.error(result.error || 'Failed to update offer status');
      }
    } catch (error) {
      console.error('Error toggling offer status:', error);
      toast.error('An error occurred while updating offer status');
    }
  };

  const deleteOffer = async (offerId: string) => {
    if (confirm('Are you sure you want to delete this loan offer? This action cannot be undone.')) {
      try {
        const result = await deleteLoanOffer(offerId);
        
        if (result.success) {
          setOffers(prev => prev.filter(offer => offer.id !== offerId));
          toast.success('Offer deleted successfully');
        } else {
          toast.error(result.error || 'Failed to delete offer');
        }
      } catch (error) {
        console.error('Error deleting offer:', error);
        toast.error('An error occurred while deleting offer');
      }
    }
  };

  const openViewModal = (offer: LoanOffer) => {
    setSelectedOffer(offer);
    setViewModalOpen(true);
  };

  const openEditModal = (offer: LoanOffer) => {
    setSelectedOffer(offer);
    setEditFormData({
      product_name: offer.product_name,
      min_amount: offer.min_amount,
      max_amount: offer.max_amount,
      min_duration: offer.min_duration,
      max_duration: offer.max_duration,
      interest_rate: offer.interest_rate,
      rate_type: offer.rate_type,
      processing_fee: offer.processing_fee,
      collateral_required: offer.collateral_required,
      description: offer.description,
      target_borrowers: offer.target_borrowers,
      status: offer.status,
      is_active: offer.is_active
    });
    setEditModalOpen(true);
  };

  const closeModals = () => {
    setViewModalOpen(false);
    setEditModalOpen(false);
    setApplicationsModalOpen(false);
    setApplicationViewModalOpen(false);
    setSelectedOffer(null);
    setEditFormData(null);
    setSelectedApplication(null);
    setApplications([]);
  };

  const fetchApplicationsForOffer = async (offerId: string) => {
    try {
      setApplicationsLoading(true);
      const result = await getLoanOfferApplications(offerId);
      
      if (result.success) {
        const rawApplications = Array.isArray(result.data) ? result.data : result.data ? [result.data] : [];
        // Filter out partial objects and ensure we only have complete LoanApplication objects
        const completeApplications = rawApplications.filter((app): app is LoanApplication => 
          app !== null && 
          typeof app === 'object' && 
          'id' in app && 
          'applicant_id' in app &&
          'loan_offer_id' in app &&
          'requested_amount' in app &&
          'status' in app
        );
        setApplications(completeApplications);
      } else {
        toast.error(result.error || 'Failed to fetch applications');
      }
    } catch (error) {
      console.error('Error fetching applications:', error);
      toast.error('An error occurred while fetching applications');
    } finally {
      setApplicationsLoading(false);
    }
  };



  const openApplicationsModal = async (offer: LoanOffer) => {
    setSelectedOffer(offer);
    setApplicationsModalOpen(true);
    await fetchApplicationsForOffer(offer.id);
  };

  const openApplicationViewModal = (application: LoanApplication) => {
    setSelectedApplication(application);
    setApplicationViewModalOpen(true);
  };

  const handleApplicationAction = async (applicationId: string, action: 'approved' | 'rejected', rejectionReason?: string) => {
    try {
      const result = await updateApplicationStatus(applicationId, action, rejectionReason);
      
      if (result.success) {
        // Update applications list
        setApplications(prev => prev.map(app => 
          app.id === applicationId 
            ? { ...app, status: action, rejection_reason: rejectionReason || null } 
            : app
        ));
        
        toast.success(`Application ${action} successfully`);
        
        if (selectedApplication && selectedApplication.id === applicationId) {
          setSelectedApplication({ 
            ...selectedApplication, 
            status: action, 
            rejection_reason: rejectionReason || null 
          });
        }
      } else {
        toast.error(result.error || `Failed to ${action.replace('ed', '')} application`);
      }
    } catch (error) {
      console.error(`Error ${action.replace('ed', 'ing')} application:`, error);
      toast.error(`An error occurred while ${action.replace('ed', 'ing')} application`);
    }
  };

  const getApplicationStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'under_review':
        return 'bg-blue-100 text-blue-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'withdrawn':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getApplicationStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />;
      case 'under_review':
        return <Eye className="w-4 h-4" />;
      case 'approved':
        return <CheckCircle className="w-4 h-4" />;
      case 'rejected':
        return <XCircle className="w-4 h-4" />;
      case 'withdrawn':
        return <X className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const handleEditSubmit = async () => {
    if (editFormData && selectedOffer) {
      try {
        setIsUpdating(true);
        const result = await updateLoanOffer(selectedOffer.id, editFormData);
        
        if (result.success) {
          setOffers(prev => prev.map(offer => 
            offer.id === selectedOffer.id ? { ...offer, ...editFormData } : offer
          ));
          toast.success('Offer updated successfully');
          closeModals();
        } else {
          toast.error(result.error || 'Failed to update offer');
        }
      } catch (error) {
        console.error('Error updating offer:', error);
        toast.error('An error occurred while updating offer');
      } finally {
        setIsUpdating(false);
      }
    }
  };

  const toggleTargetBorrower = (borrower: string) => {
    if (editFormData) {
      setEditFormData(prev => {
        if (!prev) return prev;
        const currentBorrowers = prev.target_borrowers || [];
        return {
          ...prev,
          target_borrowers: currentBorrowers.includes(borrower)
            ? currentBorrowers.filter(b => b !== borrower)
            : [...currentBorrowers, borrower]
        };
      });
    }
  };

  const stats = {
    totalOffers: offers.length,
    activeOffers: offers.filter(offer => offer.status === 'active').length,
    totalApplications: Object.values(applicationCounts).reduce((sum, count) => sum + count, 0), 
    totalDisbursed: 0
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/dashboard/lender">
            <Button variant="ghost" size="sm" className="flex items-center gap-2">
              <ArrowLeft className="w-4 h-4" />
              Back to Dashboard
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Loan Offers</h1>
            <p className="text-sm text-gray-600">Manage your loan offers and track their performance</p>
          </div>
        </div>
        <Link href="/dashboard/lender/create-offer">
          <Button className="bg-blue-600 hover:bg-blue-700 text-white">
            <Plus className="w-4 h-4 mr-2" />
            Create New Offer
          </Button>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Offers</p>
                <p className="text-2xl font-bold">{stats.totalOffers}</p>
              </div>
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <Tag className="w-5 h-5 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Offers</p>
                <p className="text-2xl font-bold">{stats.activeOffers}</p>
              </div>
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <Play className="w-5 h-5 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Applications</p>
                <p className="text-2xl font-bold">{stats.totalApplications}</p>
              </div>
              <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                <Users className="w-5 h-5 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Disbursed</p>
                <p className="text-2xl font-bold">₦{(stats.totalDisbursed / 1000000).toFixed(1)}M</p>
              </div>
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <BanknoteArrowDown className="w-5 h-5 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Offers List */}
      <Card>
        <CardHeader>
          <h2 className="text-lg font-semibold text-gray-900">Loan Offers</h2>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Loader2 className="w-8 h-8 text-gray-400 animate-spin" />
              </div>
              <p className="text-gray-600">Loading your loan offers...</p>
            </div>
          ) : offers.length === 0 ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Plus className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No loan offers yet</h3>
              <p className="text-gray-600 mb-4">Create your first loan offer to start lending to borrowers</p>
              <Link href="/dashboard/lender/create-offer">
                <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                  <Plus className="w-4 h-4 mr-2" />
                  Create Your First Offer
                </Button>
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {offers.map((offer) => (
                <div key={offer.id} className="border rounded-lg p-4 hover:shadow-sm transition-shadow">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold text-gray-900">{offer.product_name}</h3>
                        <Badge className={getStatusColor(offer.status)}>
                          <span className="flex items-center gap-1">
                            {getStatusIcon(offer.status)}
                            {offer.status.charAt(0).toUpperCase() + offer.status.slice(1)}
                          </span>
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-2">{offer.description || 'No description provided'}</p>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Amount Range:</span>
                          <p className="font-medium">₦{offer.min_amount.toLocaleString()} - ₦{offer.max_amount.toLocaleString()}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Interest Rate:</span>
                          <p className="font-medium">{offer.interest_rate}% {offer.rate_type}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Duration:</span>
                          <p className="font-medium">{offer.min_duration}-{offer.max_duration} months</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Processing Fee:</span>
                          <p className="font-medium">{offer.processing_fee}%</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2 ml-4">
                                              <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => openApplicationsModal(offer)}
                        >
                          <Users className="w-4 h-4" />
                          <span className="ml-1">
                            <span className="hidden sm:inline">Applications </span>
                            {applicationCounts[offer.id] !== undefined ? `(${applicationCounts[offer.id]})` : ''}
                          </span>
                        </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => openViewModal(offer)}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => openEditModal(offer)}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => toggleOfferStatus(offer.id)}
                      >
                        {offer.status === 'active' ? (
                          <Pause className="w-4 h-4" />
                        ) : (
                          <Play className="w-4 h-4" />
                        )}
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => deleteOffer(offer.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* View Modal */}
      <Dialog open={viewModalOpen} onOpenChange={setViewModalOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>Loan Offer Details</span>
              <Button variant="ghost" size="sm" onClick={closeModals}>
                <X className="w-4 h-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          
          {selectedOffer && (
            <div className="space-y-6">
              {/* Basic Info */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm text-gray-600">Product Name</Label>
                    <p className="font-medium">{selectedOffer.product_name}</p>
                  </div>
                  <div>
                    <Label className="text-sm text-gray-600">Status</Label>
                    <Badge className={getStatusColor(selectedOffer.status)}>
                      {selectedOffer.status.charAt(0).toUpperCase() + selectedOffer.status.slice(1)}
                    </Badge>
                  </div>
                  <div>
                    <Label className="text-sm text-gray-600">Amount Range</Label>
                    <p className="font-medium">₦{selectedOffer.min_amount.toLocaleString()} - ₦{selectedOffer.max_amount.toLocaleString()}</p>
                  </div>
                  <div>
                    <Label className="text-sm text-gray-600">Duration Range</Label>
                    <p className="font-medium">{selectedOffer.min_duration} - {selectedOffer.max_duration} months</p>
                  </div>
                  <div>
                    <Label className="text-sm text-gray-600">Interest Rate</Label>
                    <p className="font-medium">{selectedOffer.interest_rate}% {selectedOffer.rate_type}</p>
                  </div>
                  <div>
                    <Label className="text-sm text-gray-600">Processing Fee</Label>
                    <p className="font-medium">{selectedOffer.processing_fee}%</p>
                  </div>
                </div>
              </div>

              {/* Description */}
              <div>
                <Label className="text-sm text-gray-600">Description</Label>
                <p className="font-medium mt-1">{selectedOffer.description}</p>
              </div>

              {/* Target Borrowers */}
              <div>
                <Label className="text-sm text-gray-600">Target Borrowers</Label>
                <div className="flex flex-wrap gap-2 mt-1">
                  {(selectedOffer.target_borrowers || []).map((borrower) => (
                    <Badge key={borrower} variant="secondary">
                      {borrower}
                    </Badge>
                  ))}
                  {(!selectedOffer.target_borrowers || selectedOffer.target_borrowers.length === 0) && (
                    <span className="text-sm text-gray-500">No specific target borrowers</span>
                  )}
                </div>
              </div>

              {/* Requirements */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm text-gray-600">Collateral Required</Label>
                  <p className="font-medium">{selectedOffer.collateral_required ? 'Yes' : 'No'}</p>
                </div>
                <div>
                  <Label className="text-sm text-gray-600">Created Date</Label>
                  <p className="font-medium">{new Date(selectedOffer.created_at).toLocaleDateString()}</p>
                </div>
              </div>

              {/* Performance */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Performance</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div>
                  <Label className="text-sm text-gray-600">Total Applications</Label>
                  <p className="font-medium">Coming soon</p>
                </div>
                <div>
                  <Label className="text-sm text-gray-600">Total Disbursed</Label>
                  <p className="font-medium">Coming soon</p>
                </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Modal */}
      <Dialog open={editModalOpen} onOpenChange={setEditModalOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>Edit Loan Offer</span>
              <Button variant="ghost" size="sm" onClick={closeModals}>
                <X className="w-4 h-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          
          {editFormData && (
            <div className="space-y-6">
              {/* Basic Info */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
                
                <div className="space-y-2">
                  <Label htmlFor="editProductName" className="text-sm font-medium text-gray-700">
                    Product Name *
                  </Label>
                  <Input
                    id="editProductName"
                    value={editFormData.product_name || ''}
                    onChange={(e) => setEditFormData({ ...editFormData, product_name: e.target.value })}
                    className="w-full"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="editMinAmount" className="text-sm text-gray-600">
                      Minimum Amount (₦)
                    </Label>
                    <Input
                      id="editMinAmount"
                      value={editFormData.min_amount?.toString() || ''}
                      onChange={(e) => setEditFormData({ ...editFormData, min_amount: parseFloat(e.target.value) || 0 })}
                      className="w-full"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="editMaxAmount" className="text-sm text-gray-600">
                      Maximum Amount (₦)
                    </Label>
                    <Input
                      id="editMaxAmount"
                      value={editFormData.max_amount?.toString() || ''}
                      onChange={(e) => setEditFormData({ ...editFormData, max_amount: parseFloat(e.target.value) || 0 })}
                      className="w-full"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-600">Minimum Duration (months)</Label>
                    <Select
                      value={editFormData.min_duration?.toString() || ''}
                      onValueChange={(value) => setEditFormData({ ...editFormData, min_duration: parseInt(value) || 1 })}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {[1, 2, 3, 4, 5, 6].map((month) => (
                          <SelectItem key={month} value={month.toString()}>{month}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-600">Maximum Duration (months)</Label>
                    <Select
                      value={editFormData.max_duration?.toString() || ''}
                      onValueChange={(value) => setEditFormData({ ...editFormData, max_duration: parseInt(value) || 6 })}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {[6, 12, 18, 24, 36].map((month) => (
                          <SelectItem key={month} value={month.toString()}>{month}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-3">
                  <div className="flex-1">
                    <Label htmlFor="editInterestRate" className="text-sm font-medium text-gray-700">Interest Rate *</Label>
                    <Input
                      id="editInterestRate"
                      value={editFormData.interest_rate?.toString() || ''}
                      onChange={(e) => setEditFormData({ ...editFormData, interest_rate: parseFloat(e.target.value) || 0 })}
                      className="w-full"
                    />
                  </div>
                  <div className="w-full sm:w-40">
                    <Label className="text-sm font-medium text-gray-700">Rate Type</Label>
                    <Select
                      value={editFormData.rate_type || ''}
                      onValueChange={(value) => setEditFormData({ ...editFormData, rate_type: value as '% Monthly' | '% Annually' })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="% Monthly">% Monthly</SelectItem>
                        <SelectItem value="% Annually">% Annually</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="editProcessingFee" className="text-sm font-medium text-gray-700">
                    Processing Fee (%)
                  </Label>
                  <Input
                    id="editProcessingFee"
                    value={editFormData.processing_fee?.toString() || ''}
                    onChange={(e) => setEditFormData({ ...editFormData, processing_fee: parseFloat(e.target.value) || 0 })}
                    className="w-full"
                  />
                </div>
              </div>

              {/* Target Borrowers */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">Target Borrowers</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {targetBorrowerOptions.map((borrower) => (
                    <div key={borrower} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={`edit-${borrower}`}
                        checked={(editFormData.target_borrowers || []).includes(borrower)}
                        onChange={() => toggleTargetBorrower(borrower)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <Label htmlFor={`edit-${borrower}`} className="text-sm text-gray-700">{borrower}</Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Collateral Requirement */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">Collateral Requirement</Label>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="edit-collateral-required"
                      name="edit-collateral"
                      checked={editFormData.collateral_required || false}
                      onChange={() => setEditFormData({ ...editFormData, collateral_required: true })}
                      className="text-blue-600 focus:ring-blue-500"
                    />
                    <Label htmlFor="edit-collateral-required" className="text-sm text-gray-700">Collateral Required</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="edit-collateral-not-required"
                      name="edit-collateral"
                      checked={!editFormData.collateral_required}
                      onChange={() => setEditFormData({ ...editFormData, collateral_required: false })}
                      className="text-blue-600 focus:ring-blue-500"
                    />
                    <Label htmlFor="edit-collateral-not-required" className="text-sm text-gray-700">No Collateral Required</Label>
                  </div>
                </div>
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="editDescription" className="text-sm font-medium text-gray-700">
                  Loan Description
                </Label>
                <Textarea
                  id="editDescription"
                  value={editFormData.description || ''}
                  onChange={(e) => setEditFormData({ ...editFormData, description: e.target.value })}
                  className="w-full min-h-[100px] resize-none"
                />
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-3 pt-4">
                <Button variant="outline" onClick={closeModals}>
                  Cancel
                </Button>
                <Button 
                  onClick={handleEditSubmit}
                  disabled={!editFormData.product_name || isUpdating}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {isUpdating ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="w-4 h-4 mr-2" />
                  )}
                  {isUpdating ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Applications Modal */}
      <Dialog open={applicationsModalOpen} onOpenChange={setApplicationsModalOpen}>
        <DialogContent className="w-[95vw] max-w-6xl h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center justify-between">
              <span>Applications for {selectedOffer?.product_name}</span>
              <Button variant="ghost" size="sm" onClick={closeModals}>
                <X className="w-4 h-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          
          <div className="flex-1 overflow-y-auto space-y-4 pr-2">
            {applicationsLoading ? (
              <div className="text-center py-8">
                <Loader2 className="w-8 h-8 mx-auto animate-spin text-gray-400" />
                <p className="text-gray-600 mt-2">Loading applications...</p>
              </div>
            ) : applications.length === 0 ? (
              <div className="text-center py-8">
                <Users className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No applications yet</h3>
                <p className="text-gray-600">No borrowers have applied for this loan offer yet.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {applications.map((application) => (
                  <div key={application.id} className="border rounded-lg p-4 hover:shadow-sm transition-shadow">
                    {/* Application Header */}
                    <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4 mb-4">
                      <div className="flex items-center gap-3">
                        <h4 className="font-semibold text-gray-900">
                          {application.applicant_name || 'Unknown Applicant'}
                        </h4>
                        <Badge className={getApplicationStatusColor(application.status)}>
                          <span className="flex items-center gap-1">
                            {getApplicationStatusIcon(application.status)}
                            {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                          </span>
                        </Badge>
                      </div>
                      
                      <div className="flex flex-wrap items-center gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => openApplicationViewModal(application)}
                          className="flex items-center gap-1"
                        >
                          <Eye className="w-4 h-4" />
                          <span>View</span>
                        </Button>
                        
                        {application.status === 'pending' && (
                          <>
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleApplicationAction(application.id, 'approved')}
                              className="text-green-600 hover:text-green-700 flex items-center gap-1"
                            >
                              <CheckCircle className="w-4 h-4" />
                              <span>Approve</span>
                            </Button>
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => {
                                const reason = prompt('Reason for rejection (optional):');
                                if (reason !== null) {
                                  handleApplicationAction(application.id, 'rejected', reason);
                                }
                              }}
                              className="text-red-600 hover:text-red-700 flex items-center gap-1"
                            >
                              <XCircle className="w-4 h-4" />
                              <span>Reject</span>
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                    
                    {/* Application Details */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                      <div className="space-y-1">
                        <span className="text-gray-500 text-xs uppercase tracking-wide">Requested Amount</span>
                        <p className="font-medium">₦{application.requested_amount?.toLocaleString()}</p>
                      </div>
                      <div className="space-y-1">
                        <span className="text-gray-500 text-xs uppercase tracking-wide">Duration</span>
                        <p className="font-medium">{application.requested_duration} months</p>
                      </div>
                      <div className="space-y-1">
                        <span className="text-gray-500 text-xs uppercase tracking-wide">Applied Date</span>
                        <p className="font-medium">{new Date(application.created_at).toLocaleDateString()}</p>
                      </div>
                      <div className="space-y-1">
                        <span className="text-gray-500 text-xs uppercase tracking-wide">Purpose</span>
                        <p className="font-medium line-clamp-2">{application.purpose}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Application View Modal */}
      <Dialog open={applicationViewModalOpen} onOpenChange={setApplicationViewModalOpen}>
        <DialogContent className="w-[95vw] max-w-4xl h-[90vh] flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center justify-between">
              <span>Application Details</span>
              <Button variant="ghost" size="sm" onClick={closeModals}>
                <X className="w-4 h-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          
          {selectedApplication && (
            <div className="flex-1 overflow-y-auto space-y-6 pr-2">
              {/* Application Status */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 p-4 bg-gray-50 rounded-lg">
                <div className="space-y-2">
                  <h3 className="font-semibold text-gray-900">Application Status</h3>
                  <Badge className={getApplicationStatusColor(selectedApplication.status)}>
                    <span className="flex items-center gap-1">
                      {getApplicationStatusIcon(selectedApplication.status)}
                      {selectedApplication.status.charAt(0).toUpperCase() + selectedApplication.status.slice(1)}
                    </span>
                  </Badge>
                </div>
                
                {selectedApplication.status === 'pending' && (
                  <div className="flex flex-wrap gap-2">
                    <Button 
                      size="sm"
                      onClick={() => handleApplicationAction(selectedApplication.id, 'approved')}
                      className="bg-green-600 hover:bg-green-700 text-white flex items-center gap-1"
                    >
                      <CheckCircle className="w-4 h-4" />
                      Approve
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => {
                        const reason = prompt('Reason for rejection (optional):');
                        if (reason !== null) {
                          handleApplicationAction(selectedApplication.id, 'rejected', reason);
                        }
                      }}
                      className="text-red-600 hover:text-red-700 flex items-center gap-1"
                    >
                      <XCircle className="w-4 h-4" />
                      Reject
                    </Button>
                  </div>
                )}
              </div>

              {/* Loan Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Loan Details</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-600">Requested Amount</Label>
                    <p className="font-medium text-lg">₦{selectedApplication.requested_amount?.toLocaleString()}</p>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-600">Duration</Label>
                    <p className="font-medium text-lg">{selectedApplication.requested_duration} months</p>
                  </div>
                  <div className="space-y-2 sm:col-span-2 lg:col-span-1">
                    <Label className="text-sm text-gray-600">Purpose</Label>
                    <p className="font-medium">{selectedApplication.purpose}</p>
                  </div>
                </div>
              </div>

              {/* Applicant Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Applicant Information</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-600">Full Name</Label>
                    <p className="font-medium">{selectedApplication.full_name || 'To be provided'}</p>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-600">Email</Label>
                    <p className="font-medium">{selectedApplication.email || 'Not provided'}</p>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-600">Phone</Label>
                    <p className="font-medium">{selectedApplication.phone_number || 'Not provided'}</p>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-sm text-gray-600">Employment Status</Label>
                    <p className="font-medium">{selectedApplication.employment_status || 'Not provided'}</p>
                  </div>
                </div>
              </div>

              {/* Guarantor Information */}
              {(selectedApplication.guarantor_name || selectedApplication.guarantor_phone) && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Guarantor Information</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label className="text-sm text-gray-600">Guarantor Name</Label>
                      <p className="font-medium">{selectedApplication.guarantor_name || 'Not provided'}</p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm text-gray-600">Guarantor Phone</Label>
                      <p className="font-medium">{selectedApplication.guarantor_phone || 'Not provided'}</p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm text-gray-600">Guarantor Email</Label>
                      <p className="font-medium">{selectedApplication.guarantor_email || 'Not provided'}</p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm text-gray-600">Relationship</Label>
                      <p className="font-medium">{selectedApplication.guarantor_relationship || 'Not provided'}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Application Timeline */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Application Timeline</h3>
                <div className="space-y-3">
                  <div className="flex flex-col sm:flex-row sm:justify-between gap-1">
                    <span className="text-sm text-gray-600">Application Submitted:</span>
                    <span className="font-medium">{new Date(selectedApplication.created_at).toLocaleString()}</span>
                  </div>
                  {selectedApplication.updated_at && selectedApplication.updated_at !== selectedApplication.created_at && (
                    <div className="flex flex-col sm:flex-row sm:justify-between gap-1">
                      <span className="text-sm text-gray-600">Last Updated:</span>
                      <span className="font-medium">{new Date(selectedApplication.updated_at).toLocaleString()}</span>
                    </div>
                  )}
                  {selectedApplication.rejection_reason && (
                    <div className="space-y-2">
                      <span className="text-sm text-gray-600 font-medium">Rejection Reason:</span>
                      <p className="text-sm bg-red-50 p-3 rounded border border-red-200">{selectedApplication.rejection_reason}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
} 