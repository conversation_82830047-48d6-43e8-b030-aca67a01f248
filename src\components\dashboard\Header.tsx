"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Settings } from "lucide-react";
import NotificationDropdown from "./NotificationDropdown";
import { But<PERSON> } from "@/components/ui/button";
import { getCurrentUser, getUserProfile } from "@/lib/auth-supabase";

export default function Header() {
  const router = useRouter();
  const [displayName, setDisplayName] = useState<string>("User");
  const [email, setEmail] = useState<string>("");
  const [avatar, setAvatar] = useState<string>("https://randomuser.me/api/portraits/men/32.jpg");
  const [userType, setUserType] = useState<'individual' | 'corporate' | null>(null);

  // Determine base path based on user type
  const basePath = userType === 'corporate' ? '/dashboard/lender' : '/dashboard/borrower';

  const handleProfileClick = () => {
    if (userType) {
      router.push(basePath);
    }
  };

  useEffect(() => {
    async function fetchUser() {
      // First try to get user from localStorage (for fake auth)
      const storedUser = localStorage.getItem('user');
      const storedUserType = localStorage.getItem('userType');

      if (storedUser && storedUserType) {
        try {
          const user = JSON.parse(storedUser);
          setEmail(user.email || "");
          setUserType(storedUserType as 'individual' | 'corporate');

          if (storedUserType === 'individual') {
            setDisplayName(user.name || user.email || "User");
          } else if (storedUserType === 'corporate') {
            setDisplayName(user.companyName || user.name || user.email || "User");
          }
          return;
        } catch (error) {
          console.error('Error parsing stored user:', error);
        }
      }

      // Fallback to Supabase auth
      const user = await getCurrentUser();
      if (user) {
        setEmail(user.email || "");
        const profile = await getUserProfile(user.id);
        if (profile) {
          setUserType(profile.user_type);
          if (profile.user_type === "individual") {
            setDisplayName(profile.full_name || user.email || "User");
            if (profile.avatar_url) setAvatar(profile.avatar_url);
          } else if (profile.user_type === "corporate") {
            setDisplayName(profile.organization_name || user.email || "User");
            if (profile.logo_url) setAvatar(profile.logo_url);
          }
        } else {
          setDisplayName(user.email || "User");
        }
      }
    }
    fetchUser();
  }, []);

  return (
    <header className="bg-white border-b">
      <div className="flex items-center justify-between px-6 py-4">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleProfileClick}
            className="cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full"
            disabled={!userType}
          >
            <Image
              src={avatar}
              alt="User avatar"
              width={40}
              height={40}
              className={`rounded-full transition-opacity ${
                userType ? 'hover:opacity-80 cursor-pointer' : 'cursor-default'
              }`}
            />
          </button>
          <div>
            <h2 className="text-lg font-semibold">{displayName}</h2>
            <p className="text-sm text-gray-500">{email}</p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <NotificationDropdown basePath={basePath} />
          <Link href={`${basePath}/settings`}>
            <Button
              variant="ghost"
              size="icon"
              className="p-2 hover:bg-gray-100 rounded-full focus-visible:ring-0 focus-visible:ring-offset-0"
            >
              <Settings className="w-6 h-6" />
            </Button>
          </Link>
        </div>
      </div>
    </header>
  );
}
