"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Sidebar from "@/components/dashboard/Sidebar";
import Header from "@/components/dashboard/Header";
import { getCurrentUser } from "@/lib/auth-supabase";
import { User } from "@supabase/supabase-js";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);
  const router = useRouter();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        console.log('Dashboard layout: Checking authentication...');

        // First check if user data exists in localStorage (for fake auth)
        const storedUser = localStorage.getItem('user');
        const userType = localStorage.getItem('userType');

        console.log('Dashboard layout: Stored user data:', { hasUser: !!storedUser, userType });

        if (storedUser && userType) {
          // User is authenticated via fake auth system
          try {
            const userData = JSON.parse(storedUser);
            console.log('Dashboard layout: Using fake auth user:', userData);
            setUser(userData as User); // Set fake user data
            setIsLoading(false);
            return;
          } catch (error) {
            console.error('Dashboard layout: Error parsing stored user:', error);
          }
        }

        // Fallback to Supabase auth check
        console.log('Dashboard layout: Checking Supabase auth...');
        const currentUser = await getCurrentUser();

        if (!currentUser) {
          console.log('Dashboard layout: No authenticated user found, redirecting to login');
          router.push("/auth/individual/login");
          return;
        }

        console.log('Dashboard layout: Supabase user found:', currentUser);
        setUser(currentUser);
      } catch (error) {
        console.error("Dashboard layout: Auth check failed:", error);
        router.push("/auth/individual/login");
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex">
        <Sidebar />
        <div className="flex-1 flex flex-col">
          <Header />
          <main className="flex-1 p-6">{children}</main>
        </div>
      </div>
    </div>
  );
} 