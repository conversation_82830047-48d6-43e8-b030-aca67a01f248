'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { createLoanApplication, LoanApplicationData } from '@/lib/loan-applications'
import { getCurrentUser } from '@/lib/auth-supabase'
import { toast } from 'sonner'
import { Loader2, CheckCircle } from 'lucide-react'
import {
  validateEmail,
  validatePhoneNumber,
  validateName,
  validateRelationship
} from '@/lib/validation'

interface LoanOffer {
  id: string
  product_name: string
  min_amount: number
  max_amount: number
  min_duration: number
  max_duration: number
  interest_rate: number
  rate_type: string
  processing_fee: number
  collateral_required: boolean
  lender_name?: string
  lender_type?: string
}

interface LoanApplicationModalProps {
  isOpen: boolean
  onClose: () => void
  loanOffer: LoanOffer
  onSuccess?: () => void
}

export default function LoanApplicationModal({ 
  isOpen, 
  onClose, 
  loanOffer, 
  onSuccess 
}: LoanApplicationModalProps) {
  const [step, setStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [currentUser, setCurrentUser] = useState<{ email?: string } | null>(null)

  // Validation states for guarantor fields
  const [guarantorValidation, setGuarantorValidation] = useState({
    name: { isValid: true, error: '' },
    relationship: { isValid: true, error: '' },
    phone: { isValid: true, error: '' },
    email: { isValid: true, error: '' }
  })

  const [formData, setFormData] = useState<Partial<LoanApplicationData>>({
    loan_offer_id: loanOffer.id,
    requested_amount: loanOffer.min_amount,
    requested_duration: loanOffer.min_duration,
    purpose: '',
    // Set default values for required fields that we're not collecting
    full_name: 'To be provided',
    email: '',
    employment_status: 'employed',
    guarantor_name: '',
    guarantor_phone: '',
    guarantor_email: '',
    guarantor_relationship: ''
  })

  // Get current user info on mount
  useEffect(() => {
    const fetchUser = async () => {
      const user = await getCurrentUser()
      if (user) {
        setCurrentUser(user)
        setFormData(prev => ({
          ...prev,
          email: user.email || ''
        }))
      }
    }
    fetchUser()
  }, [])

  const handleInputChange = (field: keyof LoanApplicationData, value: string | number | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const validateStep = (stepNumber: number) => {
    switch (stepNumber) {
      case 1:
        const hasBasicFields = formData.requested_amount && formData.requested_duration && formData.purpose
        const isAmountValid = formData.requested_amount &&
                             formData.requested_amount >= loanOffer.min_amount &&
                             formData.requested_amount <= loanOffer.max_amount
        const isDurationValid = formData.requested_duration &&
                               formData.requested_duration >= loanOffer.min_duration &&
                               formData.requested_duration <= loanOffer.max_duration
        return hasBasicFields && isAmountValid && isDurationValid
      case 2:
        // Validate guarantor fields with proper validation
        const nameValid = validateName(formData.guarantor_name || '').isValid
        const phoneValid = validatePhoneNumber(formData.guarantor_phone || '').isValid
        const emailValid = validateEmail(formData.guarantor_email || '').isValid
        const relationshipValid = validateRelationship(formData.guarantor_relationship || '').isValid
        return nameValid && phoneValid && emailValid && relationshipValid
      default:
        return true
    }
  }

  // Get validation error messages
  const getValidationErrors = () => {
    const errors: string[] = []
    
    if (formData.requested_amount && (formData.requested_amount < loanOffer.min_amount || formData.requested_amount > loanOffer.max_amount)) {
      errors.push(`Amount must be between ₦${loanOffer.min_amount.toLocaleString()} and ₦${loanOffer.max_amount.toLocaleString()}`)
    }
    
    if (formData.requested_duration && (formData.requested_duration < loanOffer.min_duration || formData.requested_duration > loanOffer.max_duration)) {
      errors.push(`Duration must be between ${loanOffer.min_duration} and ${loanOffer.max_duration} months`)
    }
    
    return errors
  }

  const handleNext = () => {
    if (validateStep(step)) {
      setStep(step + 1)
    } else {
      const errors = getValidationErrors()
      if (errors.length > 0) {
        errors.forEach(error => toast.error(error))
      } else {
        toast.error('Please fill in all required fields')
      }
    }
  }

  const handlePrevious = () => {
    setStep(step - 1)
  }

  const handleSubmit = async () => {
    if (!validateStep(2)) {
      toast.error('Please fill in all required fields')
      return
    }

    setIsLoading(true)
    try {
      // Prepare submission data with proper defaults for required fields
      const submissionData: LoanApplicationData = {
        ...formData,
        // Set default values for required fields that we're not collecting
        full_name: formData.full_name || 'To be provided',
        email: formData.email || currentUser?.email || '<EMAIL>',
        employment_status: formData.employment_status || 'employed'
      } as LoanApplicationData

      const result = await createLoanApplication(submissionData)
      
      if (result.success) {
        setIsSubmitted(true)
        toast.success('Loan application submitted successfully!')
        onSuccess?.()
      } else {
        toast.error(result.error || 'Failed to submit application')
      }
    } catch {
      toast.error('An unexpected error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  // Guarantor field validation handlers
  const handleGuarantorNameChange = (value: string) => {
    handleInputChange('guarantor_name', value)
    const validation = validateName(value)
    setGuarantorValidation(prev => ({ ...prev, name: validation }))
  }

  const handleGuarantorRelationshipChange = (value: string) => {
    handleInputChange('guarantor_relationship', value)
    const validation = validateRelationship(value)
    setGuarantorValidation(prev => ({ ...prev, relationship: validation }))
  }

  const handleGuarantorPhoneChange = (value: string) => {
    handleInputChange('guarantor_phone', value)
    const validation = validatePhoneNumber(value)
    setGuarantorValidation(prev => ({ ...prev, phone: validation }))
  }

  const handleGuarantorEmailChange = (value: string) => {
    handleInputChange('guarantor_email', value)
    const validation = validateEmail(value)
    setGuarantorValidation(prev => ({ ...prev, email: validation }))
  }

  const handleClose = () => {
    setStep(1)
    setIsSubmitted(false)
    setFormData({
      loan_offer_id: loanOffer.id,
      requested_amount: loanOffer.min_amount,
      requested_duration: loanOffer.min_duration,
      purpose: '',
      // Set default values for required fields that we're not collecting
      full_name: 'To be provided',
      email: currentUser?.email || '',
      employment_status: 'employed',
      guarantor_name: '',
      guarantor_phone: '',
      guarantor_email: '',
      guarantor_relationship: ''
    })
    onClose()
  }

  if (isSubmitted) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-md">
          <div className="text-center py-8">
            <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">Application Submitted!</h3>
            <p className="text-gray-600 mb-6">
              Your loan application has been submitted successfully. The lender will review your application and get back to you soon.
            </p>
            <Button onClick={handleClose} className="w-full">
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Apply for {loanOffer.product_name}</DialogTitle>
          <p className="text-sm text-gray-600">
            Step {step} of 2 • {loanOffer.lender_name} ({loanOffer.lender_type})
          </p>
        </DialogHeader>

        <div className="space-y-6">
          {/* Progress Bar */}
          <div className="flex space-x-2">
            {[1, 2].map((stepNumber) => (
              <div
                key={stepNumber}
                className={`flex-1 h-2 rounded-full ${
                  stepNumber <= step ? 'bg-blue-500' : 'bg-gray-200'
                }`}
              />
            ))}
          </div>

          {/* Step 1: Loan Details */}
          {step === 1 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Loan Details</h3>
              
              {/* Validation Info */}
              {getValidationErrors().length > 0 && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <h4 className="font-semibold text-red-800 mb-2">Please fix the following:</h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    {getValidationErrors().map((error, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-red-500 mr-2">•</span>
                        {error}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="requested_amount">Requested Amount (₦)</Label>
                  <Input
                    id="requested_amount"
                    type="number"
                    min={loanOffer.min_amount}
                    max={loanOffer.max_amount}
                    value={formData.requested_amount}
                    onChange={(e) => handleInputChange('requested_amount', parseFloat(e.target.value))}
                    className={`${
                      formData.requested_amount && 
                      (formData.requested_amount < loanOffer.min_amount || formData.requested_amount > loanOffer.max_amount)
                        ? 'border-red-500 focus:ring-red-500' 
                        : ''
                    }`}
                    required
                  />
                  <p className={`text-xs mt-1 ${
                    formData.requested_amount && 
                    (formData.requested_amount < loanOffer.min_amount || formData.requested_amount > loanOffer.max_amount)
                      ? 'text-red-500' 
                      : 'text-gray-500'
                  }`}>
                    Range: ₦{loanOffer.min_amount.toLocaleString()} - ₦{loanOffer.max_amount.toLocaleString()}
                    {formData.requested_amount && 
                     (formData.requested_amount < loanOffer.min_amount || formData.requested_amount > loanOffer.max_amount) &&
                     ' • Invalid amount'
                    }
                  </p>
                </div>

                <div>
                  <Label htmlFor="requested_duration">Duration (months)</Label>
                  <Input
                    id="requested_duration"
                    type="number"
                    min={loanOffer.min_duration}
                    max={loanOffer.max_duration}
                    value={formData.requested_duration}
                    onChange={(e) => handleInputChange('requested_duration', parseInt(e.target.value))}
                    className={`${
                      formData.requested_duration && 
                      (formData.requested_duration < loanOffer.min_duration || formData.requested_duration > loanOffer.max_duration)
                        ? 'border-red-500 focus:ring-red-500' 
                        : ''
                    }`}
                    required
                  />
                  <p className={`text-xs mt-1 ${
                    formData.requested_duration && 
                    (formData.requested_duration < loanOffer.min_duration || formData.requested_duration > loanOffer.max_duration)
                      ? 'text-red-500' 
                      : 'text-gray-500'
                  }`}>
                    Range: {loanOffer.min_duration} - {loanOffer.max_duration} months
                    {formData.requested_duration && 
                     (formData.requested_duration < loanOffer.min_duration || formData.requested_duration > loanOffer.max_duration) &&
                     ' • Invalid duration'
                    }
                  </p>
                </div>
              </div>

              <div>
                <Label htmlFor="purpose">Purpose of Loan</Label>
                <Textarea
                  id="purpose"
                  value={formData.purpose}
                  onChange={(e) => handleInputChange('purpose', e.target.value)}
                  placeholder="Please describe how you plan to use this loan"
                  required
                />
              </div>

              {/* Loan Summary */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold mb-2">Loan Summary</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>Amount: ₦{formData.requested_amount?.toLocaleString()}</div>
                  <div>Duration: {formData.requested_duration} months</div>
                  <div>Interest Rate: {loanOffer.interest_rate} {loanOffer.rate_type}</div>
                  <div>Processing Fee: {loanOffer.processing_fee}%</div>
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Guarantor Information */}
          {step === 2 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Guarantor Information</h3>
              <p className="text-sm text-gray-600">
                Please provide details for your guarantor who will vouch for your loan application.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="guarantor_name">Guarantor Name *</Label>
                  <Input
                    id="guarantor_name"
                    value={formData.guarantor_name}
                    onChange={(e) => handleGuarantorNameChange(e.target.value)}
                    required
                    placeholder="Enter guarantor's full name"
                    className={!guarantorValidation.name.isValid ? 'border-red-500 focus:ring-red-500' : ''}
                  />
                  {!guarantorValidation.name.isValid && (
                    <p className="text-red-500 text-sm mt-1">{guarantorValidation.name.error}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="guarantor_phone">Guarantor Phone *</Label>
                  <Input
                    id="guarantor_phone"
                    value={formData.guarantor_phone}
                    onChange={(e) => handleGuarantorPhoneChange(e.target.value)}
                    required
                    placeholder="Enter guarantor's phone number"
                    className={!guarantorValidation.phone.isValid ? 'border-red-500 focus:ring-red-500' : ''}
                  />
                  {!guarantorValidation.phone.isValid && (
                    <p className="text-red-500 text-sm mt-1">{guarantorValidation.phone.error}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="guarantor_email">Guarantor Email *</Label>
                  <Input
                    id="guarantor_email"
                    type="email"
                    value={formData.guarantor_email}
                    onChange={(e) => handleGuarantorEmailChange(e.target.value)}
                    required
                    placeholder="Enter guarantor's email"
                    className={!guarantorValidation.email.isValid ? 'border-red-500 focus:ring-red-500' : ''}
                  />
                  {!guarantorValidation.email.isValid && (
                    <p className="text-red-500 text-sm mt-1">{guarantorValidation.email.error}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="guarantor_relationship">Relationship *</Label>
                  <Select
                    value={formData.guarantor_relationship}
                    onValueChange={(value) => handleGuarantorRelationshipChange(value)}
                  >
                    <SelectTrigger className={!guarantorValidation.relationship.isValid ? 'border-red-500 focus:ring-red-500' : ''}>
                      <SelectValue placeholder="Select relationship" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="family">Family Member</SelectItem>
                      <SelectItem value="friend">Friend</SelectItem>
                      <SelectItem value="colleague">Colleague</SelectItem>
                      <SelectItem value="business_partner">Business Partner</SelectItem>
                      <SelectItem value="mentor">Mentor</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  {!guarantorValidation.relationship.isValid && (
                    <p className="text-red-500 text-sm mt-1">{guarantorValidation.relationship.error}</p>
                  )}
                </div>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-semibold mb-2 text-blue-800">Important Note</h4>
                <p className="text-sm text-blue-700">
                  Your guarantor may be contacted to verify the information provided and confirm their willingness to guarantee this loan. 
                  Please ensure you have their consent before providing their details.
                </p>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-between pt-4 border-t">
            <Button 
              variant="outline" 
              onClick={step === 1 ? handleClose : handlePrevious}
            >
              {step === 1 ? 'Cancel' : 'Previous'}
            </Button>

            {step < 2 ? (
              <Button onClick={handleNext}>
                Next
              </Button>
            ) : (
              <Button onClick={handleSubmit} disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  'Submit Application'
                )}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
} 