"use client"

import React, { useState } from 'react';
import { Card, CardContent } from './ui/card';
import { ChevronDown, ChevronUp, Mail } from 'lucide-react';
import Image from "next/image";

const Faq = () => {
    const [openItems, setOpenItems] = useState<number[]>([]);

    const toggleItem = (index: number) => {
        setOpenItems(prev => 
            prev.includes(index) 
                ? prev.filter(i => i !== index)
                : [...prev, index]
        );
    };

    const faqItems = [
        {
            question: "How do I get started with Kredxa?",
            answer: "Getting started with Kredxa is simple. Sign up for an account, complete your profile verification, and you can start browsing loan opportunities or creating loan offers immediately."
        },
        {
            question: "What types of loans can I offer through Kredxa?",
            answer: "You can offer various types of loans including personal loans, business loans, auto loans, and more. Each loan type has specific requirements and terms that you can customize."
        },
        {
            question: "What kind of loan can I offer through Kredxa?",
            answer: "As a lender, you can offer personal loans, business loans, auto loans, education loans, and other secured or unsecured loan products based on your lending criteria."
        },
        {
            question: "How does the risk assessment work?",
            answer: "Our platform uses advanced algorithms to assess borrower risk based on credit history, income verification, employment status, and other financial indicators to help you make informed lending decisions."
        },
        {
            question: "What are the fees for using Kredxa?",
            answer: "Kredxa charges a small platform fee for successful loan transactions. There are no upfront costs, and fees are only applied when loans are successfully funded and disbursed."
        },
        {
            question: "How long does the loan approval process take?",
            answer: "The approval process typically takes 24-48 hours for most loans. This includes document verification, risk assessment, and final approval. Emergency loans may be processed faster."
        },
        {
            question: "Is my data secure on Kredxa?",
            answer: "Yes, we use bank-level security measures including SSL encryption, secure data storage, and regular security audits to protect your personal and financial information."
        },
        {
            question: "What are the fees for using Kredxa",
            answer: "Our fee structure is transparent with no hidden charges. We charge a small percentage of the loan amount as a platform fee, which covers verification, processing, and ongoing support services."
        }
    ];

    return (
        <div className="min-h-screen bg-gray-50 py-8 px-4" style={{width:'fill(1120px)', height:'hug(110px)'}}>
            <div className="max-w-4xl mx-auto">
                {/* Header */}
                <Card style={{width:'fill(1120px)', height:'hug(110px)', borderRadius:'15px',marginBottom:'37px' }}>
                    <div style={{width:'651px', height:'62px'}}>
                    <h1 style={{fontFamily:'Inter',fontStyle:'medium', fontWeight:'500', fontSize:'40px', lineHeight:'100%', color:'#230B0B', wordSpacing:'0%', paddingLeft:'10px'}}>
                        Frequently Asked Questions
                    </h1>
                    </div>
                </Card>

                {/* Help Section */}
<div className="mb-8">
  <Card className="w-full max-w-[1120px] rounded-xl shadow-sm mb-8 p-6">
    <h2 className="text-2xl font-semibold text-gray-900 mb-2">
      How can we help you?
    </h2>
    <p className="text-gray-600">
      Find answers to common questions about lending, borrower management and platform features.
    </p>
  </Card>

  {/* Category Tabs */}
  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
    <Card className="flex items-start space-x-3 p-6 shadow-sm rounded-xl hover:shadow-md transition">
      <Image
        src="/General Mandatory Action.svg"
        alt="faq"
        width={24}
        height={24}
      />
      <div>
        <h3 className="font-semibold text-gray-900">General</h3>
        <p className="text-sm text-gray-600">Basic Information and getting started</p>
      </div>
    </Card>

    <Card className="flex items-start space-x-3 p-6 shadow-sm rounded-xl hover:shadow-md transition">
      <Image
        src="/Automation.svg"
        alt="faq"
        width={24}
        height={24}
      />
      <div>
        <h3 className="font-semibold text-gray-900">Technical</h3>
        <p className="text-sm text-gray-600">API integration and technical support</p>
      </div>
    </Card>
  </div>
</div>


                {/* FAQ Section */}
                <Card className="border border-gray-200">
                    <CardContent className="p-6">
                        <h2 className="text-xl font-semibold text-gray-900 mb-6">
                            Frequently Asked Questions
                        </h2>
                    
                        <div className="space-y-4">
                            {faqItems.map((item, index) => (
                                <div key={index} className="border border-gray-200 rounded-lg">
                                    <button
                                        onClick={() => toggleItem(index)}
                                        className="w-full px-4 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                                    >
                                        <span className="font-medium text-gray-900">
                                            {item.question}
                                        </span>
                                        {openItems.includes(index) ? (
                                            <ChevronUp className="w-5 h-5 text-gray-500" />
                                        ) : (
                                            <ChevronDown className="w-5 h-5 text-gray-500" />
                                        )}
                                    </button>
                                    {openItems.includes(index) && (
                                        <div className="px-4 pb-4">
                                            <p className="text-gray-600 leading-relaxed">
                                                {item.answer}
                                            </p>
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>
                        
                    </CardContent>
                </Card>

                {/* Still have questions section */}
                <div className="mt-12 text-center">
                    <h2 className="text-3xl font-semibold text-gray-900 mb-4">
                        Still have questions?
                    </h2>
                    <p className="text-gray-600 mb-8 text-lg">
                        Our support team is here to help with any additional support questions or concern
                    </p>
                    
                    <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                        <button className="bg-[#3C1810] text-white px-8 py-3 rounded-lg flex items-center space-x-2 hover:bg-[#2d1208] transition-colors">
                            <Image
                            src="/Chat.svg"
                                alt="Live Chat"
                                width={20} 
                                height={20} 
                                
                            />                         
                            <span>Live Chat</span>
                        </button>
                        
                        <button className="bg-gray-200 text-gray-700 px-8 py-3 rounded-lg flex items-center space-x-2 hover:bg-gray-300 transition-colors">
                            <Mail className="w-39px h-39px" />
                            <span>Email Support</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default Faq;
