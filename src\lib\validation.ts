/**
 * Validation utility functions for form inputs
 */

export interface ValidationResult {
  isValid: boolean;
  error: string;
}

/**
 * Validates email address format
 * @param email - Email address to validate
 * @returns ValidationResult object with isValid boolean and optional error message
 */
export const validateEmail = (email: string): ValidationResult => {
  if (!email || email.trim() === '') {
    return { isValid: false, error: 'Email is required' };
  }

  // Basic email regex pattern
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  if (!emailRegex.test(email.trim())) {
    return { isValid: false, error: 'Please enter a valid email address' };
  }

  // Check for common invalid patterns
  const trimmedEmail = email.trim().toLowerCase();
  
  // Check for consecutive dots
  if (trimmedEmail.includes('..')) {
    return { isValid: false, error: 'Email cannot contain consecutive dots' };
  }

  // Check for invalid characters at start/end
  if (trimmedEmail.startsWith('.') || trimmedEmail.endsWith('.')) {
    return { isValid: false, error: 'Email cannot start or end with a dot' };
  }

  // Check for valid domain extension (at least 2 characters)
  const parts = trimmedEmail.split('.');
  const extension = parts[parts.length - 1];
  if (extension.length < 2) {
    return { isValid: false, error: 'Please enter a valid email domain' };
  }

  return { isValid: true, error: '' };
};

/**
 * Validates phone number format (Nigerian format)
 * @param phone - Phone number to validate
 * @returns ValidationResult object with isValid boolean and optional error message
 */
export const validatePhoneNumber = (phone: string): ValidationResult => {
  if (!phone || phone.trim() === '') {
    return { isValid: false, error: 'Phone number is required' };
  }

  // Remove all non-digit characters for validation
  const cleanPhone = phone.replace(/\D/g, '');

  // Check if phone number is empty after cleaning
  if (cleanPhone === '') {
    return { isValid: false, error: 'Phone number must contain digits' };
  }

  // Nigerian phone number validation
  // Valid formats:
  // - 11 digits starting with 0 (e.g., 08012345678)
  // - 10 digits starting with 8, 7, or 9 (e.g., 8012345678)
  // - 13 digits starting with 234 (e.g., 2348012345678)
  // - 14 digits starting with +234 (handled by removing + in cleaning)

  if (cleanPhone.length === 11) {
    // Format: 08012345678
    if (!cleanPhone.startsWith('0')) {
      return { isValid: false, error: '11-digit phone number must start with 0' };
    }
    
    const secondDigit = cleanPhone[1];
    if (!['7', '8', '9'].includes(secondDigit)) {
      return { isValid: false, error: 'Invalid Nigerian phone number format' };
    }
  } else if (cleanPhone.length === 10) {
    // Format: 8012345678
    const firstDigit = cleanPhone[0];
    if (!['7', '8', '9'].includes(firstDigit)) {
      return { isValid: false, error: 'Invalid Nigerian phone number format' };
    }
  } else if (cleanPhone.length === 13) {
    // Format: 2348012345678
    if (!cleanPhone.startsWith('234')) {
      return { isValid: false, error: '13-digit phone number must start with 234' };
    }
    
    const fourthDigit = cleanPhone[3];
    if (!['7', '8', '9'].includes(fourthDigit)) {
      return { isValid: false, error: 'Invalid Nigerian phone number format' };
    }
  } else {
    return { 
      isValid: false, 
      error: 'Phone number must be 10, 11, or 13 digits long' 
    };
  }

  return { isValid: true, error: '' };
};

/**
 * Formats phone number for display
 * @param phone - Phone number to format
 * @returns Formatted phone number string
 */
export const formatPhoneNumber = (phone: string): string => {
  const cleanPhone = phone.replace(/\D/g, '');
  
  if (cleanPhone.length === 11 && cleanPhone.startsWith('0')) {
    // Format: 0************
    return `${cleanPhone.slice(0, 4)} ${cleanPhone.slice(4, 7)} ${cleanPhone.slice(7)}`;
  } else if (cleanPhone.length === 10) {
    // Format: ************
    return `${cleanPhone.slice(0, 3)} ${cleanPhone.slice(3, 6)} ${cleanPhone.slice(6)}`;
  } else if (cleanPhone.length === 13 && cleanPhone.startsWith('234')) {
    // Format: +234 ************
    return `+${cleanPhone.slice(0, 3)} ${cleanPhone.slice(3, 6)} ${cleanPhone.slice(6, 9)} ${cleanPhone.slice(9)}`;
  }
  
  return phone; // Return original if no formatting rule matches
};

/**
 * Validates a name field (for guarantor name)
 * @param name - Name to validate
 * @returns ValidationResult object with isValid boolean and optional error message
 */
export const validateName = (name: string): ValidationResult => {
  if (!name || name.trim() === '') {
    return { isValid: false, error: 'Name is required' };
  }

  const trimmedName = name.trim();

  // Check minimum length
  if (trimmedName.length < 2) {
    return { isValid: false, error: 'Name must be at least 2 characters long' };
  }

  // Check for valid characters (letters, spaces, hyphens, apostrophes)
  const nameRegex = /^[a-zA-Z\s\-']+$/;
  if (!nameRegex.test(trimmedName)) {
    return { isValid: false, error: 'Name can only contain letters, spaces, hyphens, and apostrophes' };
  }

  // Check for consecutive spaces
  if (trimmedName.includes('  ')) {
    return { isValid: false, error: 'Name cannot contain consecutive spaces' };
  }

  return { isValid: true, error: '' };
};

/**
 * Validates relationship field
 * @param relationship - Relationship to validate
 * @returns ValidationResult object with isValid boolean and optional error message
 */
export const validateRelationship = (relationship: string): ValidationResult => {
  if (!relationship || relationship.trim() === '') {
    return { isValid: false, error: 'Relationship is required' };
  }

  const trimmedRelationship = relationship.trim();

  // Check minimum length
  if (trimmedRelationship.length < 2) {
    return { isValid: false, error: 'Relationship must be at least 2 characters long' };
  }

  // Check for valid characters (letters, spaces, hyphens)
  const relationshipRegex = /^[a-zA-Z\s\-]+$/;
  if (!relationshipRegex.test(trimmedRelationship)) {
    return { isValid: false, error: 'Relationship can only contain letters, spaces, and hyphens' };
  }

  return { isValid: true, error: '' };
};

/**
 * Real-time validation for input fields
 * @param value - Current input value
 * @param validator - Validation function to use
 * @returns ValidationResult
 */
export const validateField = (value: string, validator: (value: string) => ValidationResult): ValidationResult => {
  return validator(value);
};
