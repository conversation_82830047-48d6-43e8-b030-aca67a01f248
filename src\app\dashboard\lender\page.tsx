"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { getLenderOffers } from "@/lib/loan-offers";
import { getLoanOfferApplications } from "@/lib/loan-applications";

export default function LenderDashboard() {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [applications, setApplications] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // Stats and earnings placeholders
  const [stats, setStats] = useState([
    { label: "Total Disbursed", value: "₦0", icon: "💳" },
    { label: "Active Loans", value: 0, icon: "📊" },
    { label: "Total Earnings", value: "₦0", icon: "📁" },
    { label: "New Applications", value: 0, icon: "➕", link: "#", sub: "View All" },
  ]);
  const [earnings] = useState({ month: "₦0", disbursed: "₦0", repaid: "₦0" });
  const [portfolio] = useState({ performing: 0, overdue: 0, repayments: "₦0", percent: 0 });

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      // Fetch offers
      const offersResult = await getLenderOffers();
      if (offersResult.success && Array.isArray(offersResult.data)) {
        // Example: Calculate stats from offers
        setStats([
          { label: "Total Disbursed", value: `₦${offersResult.data.reduce((sum, o) => sum + (o.max_amount || 0), 0).toLocaleString()}`, icon: "💳" },
          { label: "Active Loans", value: offersResult.data.length, icon: "📊" },
          { label: "Total Earnings", value: "₦0", icon: "📁" }, // TODO: Replace with real earnings
          { label: "New Applications", value: 0, icon: "➕", link: "#", sub: "View All" }, // TODO: Replace with real count
        ]);
      }
      // Fetch applications for the first offer (as an example)
      if (offersResult.success && offersResult.data.length > 0) {
        const appResult = await getLoanOfferApplications(offersResult.data[0].id);
        if (appResult.success && Array.isArray(appResult.data)) {
          setApplications(appResult.data);
          // Example: Update new applications stat
          setStats(prev =>
            prev.map(s =>
              s.label === "New Applications"
                ? { ...s, value: Array.isArray(appResult.data) ? appResult.data.length : 0 }
                : s
            )
          );
        }
      }
      // TODO: Fetch and set real earnings and portfolio data
      setLoading(false);
    }
    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Portfolio Overview & Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Portfolio Overview */}
        <div className="bg-white rounded-3xl shadow p-8 flex flex-col justify-between">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold">Portfolio Overview</h2>
            <Link href="/dashboard/lender/portfolio" className="text-black font-semibold hover:underline">View Portfolio</Link>
          </div>
          <div className="flex gap-6 mb-6">
            <div className="flex-1 flex flex-col items-center justify-center bg-green-50 rounded-2xl border border-green-100 py-6">
              <div className="text-4xl font-bold text-green-600">{portfolio.performing}</div>
              <div className="text-lg text-green-600">Performing Loans</div>
            </div>
            <div className="flex-1 flex flex-col items-center justify-center bg-red-50 rounded-2xl border border-red-100 py-6">
              <div className="text-4xl font-bold text-red-600">{portfolio.overdue}</div>
              <div className="text-lg text-red-600">Overdue</div>
            </div>
          </div>
          <div className="mb-2 text-lg font-medium text-gray-700">Total Repayments <span className="float-right font-bold text-black">{portfolio.repayments}</span></div>
          <div className="w-full h-4 bg-gray-200 rounded-full mb-1">
            <div className="h-4 bg-green-500 rounded-full" style={{ width: `${portfolio.percent}%` }}></div>
          </div>
          <div className="text-sm text-gray-500 text-right">{portfolio.percent}% of total portfolio</div>
        </div>
        {/* Quick Actions */}
        <div className="bg-white rounded-3xl shadow p-8 flex flex-col justify-between">
          <h2 className="text-2xl font-bold mb-6">Quick Actions</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Link href="/dashboard/lender/create-offer" className="flex flex-col items-center justify-center bg-blue-50 rounded-2xl border border-blue-100 py-6 cursor-pointer hover:shadow-lg transition">
              <span className="text-3xl text-blue-500 mb-2">+</span>
              <span className="font-medium text-lg">Create Loan Offer</span>
            </Link>
            <div className="flex flex-col items-center justify-center bg-purple-50 rounded-2xl border border-purple-100 py-6 cursor-pointer hover:shadow-lg transition">
              <span className="text-3xl text-purple-500 mb-2">&#128101;</span>
              <span className="font-medium text-lg">View Applications</span>
            </div>
            <div className="flex flex-col items-center justify-center bg-green-50 rounded-2xl border border-green-100 py-6 cursor-pointer hover:shadow-lg transition">
              <span className="text-3xl text-green-500 mb-2">&#8599;</span>
              <span className="font-medium text-lg">Withdraw Earnings</span>
            </div>
            <Link href="/dashboard/lender/reports" className="flex flex-col items-center justify-center bg-orange-50 rounded-2xl border border-orange-100 py-6 cursor-pointer hover:shadow-lg transition">
              <span className="text-3xl text-orange-500 mb-2">📈</span>
              <span className="font-medium text-lg">Reports & Earnings</span>
            </Link>
          </div>
        </div>
      </div>
      {/* Welcome Card */}
      <div className="bg-white rounded-xl shadow p-6 mb-2">
        <h2 className="text-2xl font-bold mb-1">Welcome back, Jane!</h2>
        <p className="text-gray-600 text-sm">Your lending portfolio is performing well with {stats[1].value} active loans and {stats[2].value} in total earnings.</p>
        <div className="mt-4">
          <Link href="/dashboard/lender/my-offers">
            <Button variant="outline" size="sm" className="text-blue-600 hover:text-blue-700">
              View All My Offers
            </Button>
          </Link>
        </div>
      </div>
      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-2">
        {stats.map((stat) => (
          <div key={stat.label} className="bg-white rounded-xl shadow p-4 flex flex-col items-start relative">
            <div className="text-2xl mb-2">{stat.icon}</div>
            <div className="text-lg font-semibold">{stat.value}</div>
            <div className="text-xs text-gray-500">{stat.label}</div>
            {stat.link && (
              <a href={stat.link} className="absolute top-3 right-4 text-xs text-black hover:underline">{stat.sub}</a>
            )}
          </div>
        ))}
      </div>
      {/* Main Content: Applications & Earnings */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Latest Borrower Applications */}
        <div className="md:col-span-2 bg-white rounded-xl shadow p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Latest Borrower Applications</h3>
            <a href="#" className="text-black text-sm hover:underline">View All</a>
          </div>
          <div className="space-y-4">
            {applications.map((app, idx) => (
              <div key={app.id || idx} className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                <div className="flex items-center gap-3">
                  {/* No avatar in real data, fallback to name initial */}
                  <div className="w-10 h-10 flex items-center justify-center rounded-full border-2 border-gray-200 bg-gray-100 text-xl font-bold text-gray-700">
                    {app.full_name?.charAt(0)?.toUpperCase() || "?"}
                  </div>
                  <div>
                    <div className="font-medium">{app.full_name || app.email}</div>
                    <div className="text-xs text-gray-500">₦{app.requested_amount?.toLocaleString()} • {app.purpose}</div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <span className={`text-xs px-2 py-1 rounded bg-yellow-100 text-yellow-700`}>{app.status}</span>
                  <button className="bg-black text-white px-4 py-1.5 rounded hover:bg-gray-800 text-sm font-medium">Review</button>
                </div>
              </div>
            ))}
          </div>
        </div>
        {/* Earnings Snapshot */}
        <div className="bg-white rounded-xl shadow p-6 flex flex-col justify-between">
          <div>
            <h3 className="text-lg font-semibold mb-4">Earnings Snapshot</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between"><span>This Month</span><span className="font-semibold">{earnings.month}</span></div>
              <div className="flex justify-between"><span>Disbursed</span><span className="text-green-600 font-semibold">{earnings.disbursed}</span></div>
              <div className="flex justify-between"><span>Repaid</span><span className="text-blue-600 font-semibold">{earnings.repaid}</span></div>
            </div>
          </div>
          <button className="mt-6 w-full bg-gray-100 text-gray-700 py-2 rounded hover:bg-gray-200 font-medium">View Full Report</button>
        </div>
      </div>
    </div>
  );
}