import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import Image from 'next/image';

const Page = () => {
    const investments2 = [
        {
            id: "Total Invested",
            icon: "/calendar.svg",
            amt:"30,459.50",
        },
        {
            id: "Active Loans",
            icon:"/database.svg",
            num:"23",
        },
        {
            id: "Monthly Returns",
            icon:"/returnbox.svg",
            amt:"2,840",
        }
    ];
    const investments = [
        {
            name: "<PERSON>",
            loanType: "Business Loan",
            apr: "12.5% APR",
            amount: "₦5,230",
            timeLeft: "24 months left"
        },
        {
            name: "<PERSON>",
            loanType: "Personal Loan",
            apr: "8.5% APR",
            amount: "₦5,230",
            timeLeft: "12 months left"
        },
        {
            name: "<PERSON>",
            loanType: "Auto Loan",
            apr: "6.8% APR",
            amount: "₦5,230",
            timeLeft: "18 months left"
        },
        {
            name: "<PERSON>",
            loanType: "Business Loan",
            apr: "12.5% APR",
            amount: "₦5,230",
            timeLeft: "36 months left"
        }
    ];

    return (
        <div className="space-y-6">
            {/* Header */}
            <div>
                <h1 className="text-3xl font-bold text-gray-900">Portfolio Overview</h1>
                <p className="text-gray-600 mt-1">Manage and track your lending investments</p>
            </div>
            {/* Portfolio Stats */}
            <Card className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {investments2.map((investment, index) => (
                    <Card key={index} className="bg-gray-50 border border-gray-200 rounded-lg shadow-sm ">
                        <CardContent className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="font-semibold text-gray-900">{investment.id}</h3>
                                    <div className="text-2xl font-bold text-gray-900 mt-2">
                                        {investment.amt !== undefined
                                            ? `₦${investment.amt}`
                                            : investment.num}
                                    </div>
                                </div>
                                <div className="p-3 rounded-lg bg-green-100">
                                    <Image
                                        src={investment.icon}
                                        alt={investment.id}
                                        width={32}
                                        height={32}
                                        className="object-contain"
                                    />
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </Card>
            {/* Active Investments */}
            <Card className="bg-white shadow-sm">
                <CardHeader>
                    <CardTitle className="text-2xl font-bold text-gray-900">Active Investments</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        {investments.map((investment, index) => (
                            <Card key={index} className="border border-gray-200 rounded-lg">
                                <CardContent className="p-4">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-4">
                                            <div className="relative">
                                                <div className="w-12 h-12 rounded-full overflow-hidden border-4 border-gray-800">
                                                    <Image
                                                        src="/portfoliopic.jpg"
                                                        alt={investment.name}
                                                        width={48}
                                                        height={48}
                                                        className="object-cover w-full h-full"
                                                    />
                                                </div>
                                            </div>
                                            <div>
                                                <h3 className="font-semibold text-gray-900">{investment.name}</h3>
                                                <div className="flex items-center space-x-2 text-sm text-gray-600">
                                                    <span>{investment.loanType}</span>
                                                    <span className="font-bold">•</span>
                                                    <span>{investment.apr}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <div className="font-semibold text-gray-900">{investment.amount}</div>
                                            <div className="text-sm text-gray-600">{investment.timeLeft}</div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}

export default Page;



