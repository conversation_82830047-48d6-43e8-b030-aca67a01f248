import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import Image from 'next/image';

const Page = () => {
    const investments2 = [
        {
            id: "Total Invested",
            icon: "/calendar.svg",
            amt:"30,459.50",
        },
        {
            id: "Active Loans",
            icon:"/database.svg",
            num:"23",
        },
        {
            id: "Monthly Returns",
            icon:"/returnbox.svg",
            amt:"2,840",
        }
    ];
    const investments = [
        {
            name: "<PERSON>",
            loanType: "Business Loan",
            apr: "12.5% APR",
            amount: "₦5,230",
            timeLeft: "24 months left"
        },
        {
            name: "<PERSON>",
            loanType: "Personal Loan", 
            apr: "8.5% APR",
            amount: "₦5,230",
            timeLeft: "12 months left"
        },
        {
            name: "<PERSON>",
            loanType: "Auto Loan",
            apr: "6.8% APR", 
            amount: "₦5,230",
            timeLeft: "18 months left"
        },
        {
            name: "<PERSON>",
            loanType: "Business Loan",
            apr: "12.5% APR",
            amount: "₦5,230", 
            timeLeft: "36 months left"
        }
    ];


    return (
        <>
        <div className="min-h-screen bg-gray-50 flex items-center justify-left p-4">
            <div style={{flexFlow:'vertical', width:'fixed(590px)', height:'hug(104px)', paddingLeft:'5px', paddingRight:'5px'}}>
                <div style={{width:'449px', height:'62px', left:'5px'}}>
                    <p style={{fontFamily:'Inter',fontStyle:'normal', fontWeight:'400', fontSize:'40px', lineHeight:'100%', color:'#230B0B', wordSpacing:'0%', verticalAlign:'middle'}}>Portfolio Overview</p>
                    <p style={{fontFamily:'Inter',fontStyle:'light', fontWeight:'300', fontSize:'24px', lineHeight:'21px', color:'#230B0B', wordSpacing:'0%', verticalAlign:'middle'}}>Manage and track your lending investments</p>
                </div>
                <Card style={{flexFlow:'vertical', width:'fill(1161px)', height:'fixed(1088px)',borderRadius:'15px',paddingTop:'20px', paddingLeft:'30px', paddingRight:'30px'}}>
                    <Card style={{width:'1101px',height:'full',borderRadius:'15px',border:'1px', padding:'1rem'}}>
                        {/* First card */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {investments2.map((investment, index) => (
                            <Card key={index} style={{backgroundColor:'#F5F5F599', border:"0.5px"}}className="border border-gray-200 rounded-lg p-4 shadow-none">
                            <div className="flex items-center justify-between">
                                <div>
                                <h3 className="font-semibold text-gray-900">{investment.id}</h3>
                                <div className="text-sm text-gray-500">
                                    <span>
                                    {investment.amt !== undefined
                                        ? `₦${investment.amt}`
                                        : investment.num}
                                    </span>
                                </div>
                                </div>
                                <div className="p-2 rounded bg-green-100">
                                <Image
                                    src={investment.icon}
                                    alt={investment.id}
                                    width={32}
                                    height={32}
                                    className="object-contain"
                                />
                                </div>
                            </div>
                            </Card>
                        ))}
                        </div>

                        {/* Second card */}
                    </Card>
                    <p style={{fontFamily:'Inter',fontStyle:'normal', fontWeight:'400', fontSize:'32px', lineHeight:'21px', color:'#121417', wordSpacing:'0px', verticalAlign:'middle'}}>Active Investments</p>
                    
                    <div className="space-y-4 mt-6">
                        {investments.map((investment, index) => (
                            <Card key={index} className="border border-gray-200 rounded-lg p-4">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <div className="relative" style={{ top: '0' }}>
                                            <div 
                                                className="rounded-full overflow-hidden flex items-center justify-center"
                                                style={{
                                                    width: '54px',
                                                    height: '54px',
                                                    border: '8px solid #230B0B',
                                                    transform: 'rotate(180deg)'
                                                }}
                                            >
                                                <Image 
                                                    src="/portfoliopic.jpg"
                                                    alt={investment.name}
                                                    width={54}
                                                    height={54}
                                                    className="object-cover w-full h-full"
                                                    style={{ transform: 'rotate(180deg)' }}
                                                />
                                            </div>
                                        </div>
                                        <div>
                                            <h3 style={{ color: '#121417' }} className="font-inter font-normal ">{investment.name}</h3>
                                            <div style={{ color: '#8D938CB2' }}className="flex items-center space-x-2 text-sm">
                                                <span>{investment.loanType}</span>
                                                <span style={{color:'#0B0A0A'}} className="font-very-bold">•</span>
                                                <span>{investment.apr}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <div style={{ color: '#121417' }}className="font-semibold">{investment.amount}</div>
                                        <div style={{ color: '#8D938CB2' }} className="text-sm ">{investment.timeLeft}</div>
                                    </div>
                                </div>
                            </Card>
                        ))}
                    </div>
                </Card>
            </div>
        </div>
        </>
    );
}

export default Page;



